# AI Trainer Platform - Design Document

## Overview

The AI Trainer Platform is a Node.js-based learning management system that integrates OpenAI Assistant API for intelligent training conversations. The system uses MySQL for data persistence and implements a multi-role architecture supporting Super Admins, Admins, and Members. The platform tracks training progress through structured JSON responses from AI trainers containing message, progress percentage, and scores.

## Architecture

### System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Interface]
        API_CLIENT[API Client]
    end
    
    subgraph "Application Layer"
        AUTH[Authentication Service]
        TRAINER[Trainer Management Service]
        CHAT[Chat Service]
        PROGRESS[Progress Tracking Service]
        MONITOR[Monitoring Service]
    end
    
    subgraph "Integration Layer"
        OPENAI[OpenAI Assistant API]
        WEBSOCKET[WebSocket Service]
    end
    
    subgraph "Data Layer"
        MYSQL[(MySQL Database)]
        REDIS[(Redis Cache)]
    end
    
    WEB --> API_CLIENT
    API_CLIENT --> AUTH
    API_CLIENT --> TRAINER
    API_CLIENT --> CHAT
    API_CLIENT --> PROGRESS
    API_CLIENT --> <PERSON><PERSON><PERSON><PERSON>
    
    CHAT --> OPENAI
    MONITOR --> WE<PERSON>OCKET
    
    AUTH --> MYS<PERSON>
    TRAINER --> MYS<PERSON>
    CHAT --> MYSQL
    PROGRESS --> MYSQL
    MONITOR --> MYSQL
    
    CHAT --> REDIS
    PROGRESS --> REDIS
```

### Technology Stack

- **Backend Framework**: Node.js with Express.js
- **Frontend Framework**: React.js with TypeScript
- **UI Library**: Tailwind CSS with Headless UI components
- **Mobile Framework**: Progressive Web App (PWA) with responsive design
- **State Management**: Zustand for lightweight state management
- **Database**: MySQL 8.0+ with connection pooling
- **Caching**: Redis for session management and real-time data
- **AI Integration**: OpenAI Assistant API
- **Real-time Communication**: Socket.IO for live monitoring
- **Authentication**: JWT tokens with role-based access control
- **ORM**: Sequelize for MySQL operations
- **API Documentation**: Swagger/OpenAPI 3.0

## Components and Interfaces

### Authentication Service

**Purpose**: Handle user authentication and authorization across all user roles.

**Key Methods**:
- `authenticateUser(email, password, userType)`: Validate credentials
- `generateJWT(user, role)`: Create JWT tokens with role claims
- `validateToken(token)`: Verify and decode JWT tokens
- `checkPermissions(user, resource, action)`: Role-based access control

**Security Features**:
- Password hashing using bcrypt (12 rounds)
- JWT token expiration (24 hours)
- Rate limiting for login attempts
- Session invalidation on logout

### Trainer Management Service

**Purpose**: Handle CRUD operations for AI trainer modules, sub-modules, and assignments.

**Key Methods**:
- `createTrainer(adminId, name, description)`: Create new trainer module container
- `createSubmodule(trainerId, name, systemPrompt, orderIndex)`: Add sub-module to trainer
- `updateTrainer(trainerId, updates)`: Modify existing trainer module
- `updateSubmodule(submoduleId, updates)`: Modify existing sub-module
- `assignTrainerToMember(trainerId, memberId, adminId)`: Create assignments
- `getTrainersByAdmin(adminId)`: Retrieve admin's trainers with sub-modules
- `getTrainersByMember(memberId)`: Get member's assigned trainers with sub-modules
- `getSubmodulesByTrainer(trainerId)`: Retrieve ordered sub-modules for a trainer
- `reorderSubmodules(trainerId, submoduleIds)`: Update sub-module order

**Business Logic**:
- System prompt validation for OpenAI compatibility at sub-module level
- Multi-admin assignment support
- Trainer and sub-module status management (active/inactive)
- Sub-module ordering and progression logic

### Chat Service

**Purpose**: Manage conversations between members and AI trainers with progress tracking at sub-module level.

**Key Methods**:
- `initializeConversation(trainerId, submoduleId, memberId)`: Start new chat session for specific sub-module
- `sendMessage(conversationId, message, sender)`: Process member messages
- `getAIResponse(submoduleSystemPrompt, conversationHistory)`: Call OpenAI Assistant with sub-module context
- `parseAIResponse(response)`: Extract message, progress, and score
- `saveConversation(conversationId, message, metadata)`: Store chat data with sub-module reference
- `getConversationsBySubmodule(submoduleId, memberId)`: Retrieve sub-module specific conversations
- `switchSubmodule(trainerId, newSubmoduleId, memberId)`: Change active sub-module in conversation

**OpenAI Integration**:
```javascript
// Expected AI Response Format
{
  "message": "Great progress! Let's move to the next topic...",
  "progress": 75,
  "score": 8.5
}
```

### Progress Tracking Service

**Purpose**: Monitor and analyze member training progress across modules.

**Key Methods**:
- `updateProgress(trainerId, memberId, progress, score)`: Store progress data
- `getProgressByMember(memberId)`: Retrieve member's overall progress
- `getProgressByTrainer(trainerId)`: Get trainer-specific progress data
- `generateProgressReport(adminId, filters)`: Create progress summaries
- `getProgressTrends(memberId, timeRange)`: Analyze progress over time

**Analytics Features**:
- Progress percentage tracking
- Score history and trends
- Completion rate calculations
- Performance comparisons

### Monitoring Service

**Purpose**: Provide real-time monitoring capabilities for admins and super admins.

**Key Methods**:
- `getActiveConversations(adminId)`: Show live chat sessions
- `streamConversationUpdates(adminId)`: WebSocket updates
- `getAdminDashboard(adminId)`: Comprehensive admin view
- `getSuperAdminDashboard()`: System-wide overview
- `filterMonitoringData(filters)`: Apply search and filter criteria

**Real-time Features**:
- Live conversation monitoring via WebSocket
- Real-time progress updates
- Instant notification of completed training sessions

## Data Models

### Database Schema

```sql
-- Users and Roles
CREATE TABLE admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email)
);

CREATE TABLE members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email)
);

-- Multi-admin support
CREATE TABLE admin_member (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_id INT NOT NULL,
    member_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    UNIQUE KEY unique_admin_member (admin_id, member_id),
    INDEX idx_admin (admin_id),
    INDEX idx_member (member_id)
);

-- Trainer modules (parent containers)
CREATE TABLE trainers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT DEFAULT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE,
    INDEX idx_admin (admin_id),
    INDEX idx_status (status)
);

-- Sub-modules within trainer modules
CREATE TABLE trainer_submodules (
    id INT PRIMARY KEY AUTO_INCREMENT,
    trainer_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    system_prompt TEXT NOT NULL,
    order_index INT NOT NULL DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (trainer_id) REFERENCES trainers(id) ON DELETE CASCADE,
    INDEX idx_trainer (trainer_id),
    INDEX idx_status (status),
    INDEX idx_order (order_index)
);

-- Trainer assignments
CREATE TABLE trainer_member (
    id INT PRIMARY KEY AUTO_INCREMENT,
    trainer_id INT NOT NULL,
    member_id INT NOT NULL,
    assigned_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trainer_id) REFERENCES trainers(id) ON DELETE CASCADE,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES admins(id) ON DELETE CASCADE,
    UNIQUE KEY unique_trainer_member (trainer_id, member_id),
    INDEX idx_trainer (trainer_id),
    INDEX idx_member (member_id),
    INDEX idx_assigned_by (assigned_by)
);

-- Conversations (linked to sub-modules)
CREATE TABLE trainer_conversations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    trainer_id INT NOT NULL,
    submodule_id INT NOT NULL,
    member_id INT NOT NULL,
    message_from ENUM('member', 'ai') NOT NULL,
    message TEXT NOT NULL,
    ai_progress INT DEFAULT NULL,
    ai_score DECIMAL(3,1) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trainer_id) REFERENCES trainers(id) ON DELETE CASCADE,
    FOREIGN KEY (submodule_id) REFERENCES trainer_submodules(id) ON DELETE CASCADE,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    INDEX idx_trainer_member (trainer_id, member_id),
    INDEX idx_submodule_member (submodule_id, member_id),
    INDEX idx_created_at (created_at),
    INDEX idx_message_from (message_from)
);

-- Progress tracking (linked to sub-modules)
CREATE TABLE trainer_scores (
    id INT PRIMARY KEY AUTO_INCREMENT,
    trainer_id INT NOT NULL,
    submodule_id INT NOT NULL,
    member_id INT NOT NULL,
    progress INT NOT NULL DEFAULT 0,
    score DECIMAL(3,1) DEFAULT NULL,
    notes TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (trainer_id) REFERENCES trainers(id) ON DELETE CASCADE,
    FOREIGN KEY (submodule_id) REFERENCES trainer_submodules(id) ON DELETE CASCADE,
    FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE CASCADE,
    INDEX idx_trainer_member (trainer_id, member_id),
    INDEX idx_submodule_member (submodule_id, member_id),
    INDEX idx_progress (progress),
    INDEX idx_created_at (created_at)
);
```

### Sequelize Models

```javascript
// Admin Model
const Admin = sequelize.define('Admin', {
  name: { type: DataTypes.STRING, allowNull: false },
  email: { type: DataTypes.STRING, unique: true, allowNull: false },
  password: { type: DataTypes.STRING, allowNull: false }
});

// Member Model
const Member = sequelize.define('Member', {
  name: { type: DataTypes.STRING, allowNull: false },
  email: { type: DataTypes.STRING, unique: true, allowNull: false },
  password: { type: DataTypes.STRING, allowNull: false }
});

// Trainer Model (parent container)
const Trainer = sequelize.define('Trainer', {
  name: { type: DataTypes.STRING, allowNull: false },
  description: { type: DataTypes.TEXT, allowNull: true },
  status: { type: DataTypes.ENUM('active', 'inactive'), defaultValue: 'active' }
});

// TrainerSubmodule Model
const TrainerSubmodule = sequelize.define('TrainerSubmodule', {
  name: { type: DataTypes.STRING, allowNull: false },
  systemPrompt: { type: DataTypes.TEXT, allowNull: false },
  orderIndex: { type: DataTypes.INTEGER, defaultValue: 0 },
  status: { type: DataTypes.ENUM('active', 'inactive'), defaultValue: 'active' }
});

// Associations
Admin.belongsToMany(Member, { through: 'admin_member' });
Member.belongsToMany(Admin, { through: 'admin_member' });
Admin.hasMany(Trainer);
Trainer.belongsTo(Admin);
Trainer.hasMany(TrainerSubmodule);
TrainerSubmodule.belongsTo(Trainer);
Trainer.belongsToMany(Member, { through: 'trainer_member' });
Member.belongsToMany(Trainer, { through: 'trainer_member' });
```

## Error Handling

### API Error Response Format

```javascript
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input parameters",
    "details": {
      "field": "email",
      "reason": "Email format is invalid"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Error Categories

1. **Authentication Errors**: Invalid credentials, expired tokens
2. **Authorization Errors**: Insufficient permissions, role violations
3. **Validation Errors**: Invalid input data, missing required fields
4. **OpenAI API Errors**: Rate limits, API failures, invalid responses
5. **Database Errors**: Connection failures, constraint violations
6. **Business Logic Errors**: Invalid assignments, inactive trainers

### Error Handling Strategy

- Global error middleware for consistent error responses
- Structured logging with correlation IDs
- Graceful degradation for OpenAI API failures
- Retry mechanisms with exponential backoff
- Circuit breaker pattern for external API calls

## Testing Strategy

### Unit Testing

- **Framework**: Jest with Supertest for API testing
- **Coverage Target**: 90% code coverage
- **Mock Strategy**: Mock external dependencies (OpenAI API, database)
- **Test Categories**:
  - Service layer business logic
  - Database model validations
  - Authentication and authorization
  - API endpoint responses

### Integration Testing

- **Database Testing**: In-memory MySQL for isolated tests
- **API Testing**: Full request/response cycle testing
- **OpenAI Integration**: Mock API responses with realistic data
- **WebSocket Testing**: Socket.IO client simulation

### Performance Testing

- **Load Testing**: Artillery.js for API endpoint stress testing
- **Database Performance**: Query optimization and index validation
- **Concurrent User Testing**: Multiple simultaneous chat sessions
- **Memory Leak Detection**: Long-running process monitoring

### Test Data Management

```javascript
// Test fixtures for consistent testing
const testData = {
  admin: {
    name: "Test Admin",
    email: "<EMAIL>",
    password: "hashedPassword123"
  },
  member: {
    name: "Test Member",
    email: "<EMAIL>",
    password: "hashedPassword456"
  },
  trainer: {
    name: "JavaScript Fundamentals",
    systemPrompt: "You are a JavaScript tutor...",
    status: "active"
  },
  aiResponse: {
    message: "Great job! You're making progress.",
    progress: 65,
    score: 7.5
  }
};
```

## Performance Optimizations

### Database Optimizations

- **Connection Pooling**: MySQL connection pool (min: 5, max: 20)
- **Query Optimization**: Proper indexing on frequently queried columns
- **Pagination**: Limit large result sets with cursor-based pagination
- **Caching Strategy**: Redis for frequently accessed data

### API Performance

- **Response Compression**: Gzip compression for API responses
- **Rate Limiting**: Per-user and per-endpoint rate limits
- **Caching Headers**: Appropriate cache-control headers
- **Request Validation**: Early validation to prevent unnecessary processing

### Real-time Features

- **WebSocket Optimization**: Connection pooling and message batching
- **Event Debouncing**: Prevent excessive real-time updates
- **Selective Broadcasting**: Send updates only to relevant users

### OpenAI API Optimization

- **Request Batching**: Combine multiple requests where possible
- **Response Caching**: Cache similar conversation contexts
- **Timeout Management**: Appropriate timeouts with fallback responses
- **Rate Limit Handling**: Intelligent retry with exponential backoff

## Frontend Design & User Interface

### Design System

**Mobile-First Approach**: All interfaces are designed with mobile as the primary platform, then enhanced for tablet and desktop.

**Design Principles**:
- Clean, modern interface with minimal cognitive load
- Dark/Light theme support with system preference detection
- Consistent spacing using 8px grid system
- Accessible color contrast ratios (WCAG AA compliant)
- Touch-friendly interactive elements (minimum 44px tap targets)

**Color Palette**:
```css
/* Primary Colors */
--primary-50: #eff6ff;
--primary-500: #3b82f6;
--primary-600: #2563eb;
--primary-700: #1d4ed8;

/* Neutral Colors */
--gray-50: #f9fafb;
--gray-100: #f3f4f6;
--gray-500: #6b7280;
--gray-900: #111827;

/* Status Colors */
--success-500: #10b981;
--warning-500: #f59e0b;
--error-500: #ef4444;
```

### Member Dashboard Interface

**Layout Structure**:
```
┌─────────────────────────────────┐
│ Header (Profile, Notifications) │
├─────────────────────────────────┤
│ Progress Overview Cards         │
├─────────────────────────────────┤
│ Assigned Training Modules       │
│ ┌─────────────────────────────┐ │
│ │ Module Card                 │ │
│ │ ├─ Sub-modules List         │ │
│ │ ├─ Progress Bar             │ │
│ │ └─ Continue/Start Button    │ │
│ └─────────────────────────────┘ │
├─────────────────────────────────┤
│ Recent Activity Feed            │
└─────────────────────────────────┘
```

**Key Components**:

1. **Training Module Cards**:
   - Modern card design with subtle shadows and rounded corners
   - Progress visualization with animated progress bars
   - Sub-module expansion with smooth animations
   - Quick action buttons (Continue, Start, Review)

2. **Chat Interface**:
   - WhatsApp-style conversation bubbles
   - Real-time typing indicators
   - Progress updates displayed as system messages
   - Voice input support for mobile devices
   - Swipe gestures for navigation between sub-modules

3. **Progress Tracking**:
   - Circular progress indicators with animated fills
   - Achievement badges and milestones
   - Score history with interactive charts (Chart.js)
   - Completion certificates

**Mobile Optimizations**:
- Bottom navigation for easy thumb access
- Swipe gestures for module navigation
- Pull-to-refresh functionality
- Offline mode with sync when connected
- Push notifications for progress updates

### Admin Dashboard Interface

**Layout Structure**:
```
┌─────────────────────────────────┐
│ Header (Search, Profile, Menu)  │
├─────────────────────────────────┤
│ Analytics Overview (Cards)      │
├─────────────────────────────────┤
│ Quick Actions Bar               │
├─────────────────────────────────┤
│ Main Content Area               │
│ ├─ Trainer Management          │
│ ├─ Member Monitoring           │
│ ├─ Real-time Conversations     │
│ └─ Progress Analytics          │
└─────────────────────────────────┘
```

**Key Features**:

1. **Trainer Module Builder**:
   - Drag-and-drop interface for sub-module ordering
   - Rich text editor for system prompts with syntax highlighting
   - Live preview of AI responses
   - Template library for common training scenarios

2. **Member Monitoring Dashboard**:
   - Real-time conversation streams with live updates
   - Filterable member list with search and sorting
   - Progress heatmaps and trend analysis
   - Bulk actions for member management

3. **Analytics & Reporting**:
   - Interactive charts and graphs (Chart.js/D3.js)
   - Exportable reports (PDF/Excel)
   - Custom date range selectors
   - Performance metrics and KPIs

**Responsive Breakpoints**:
```css
/* Mobile First */
@media (min-width: 640px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
```

### Component Library

**Reusable Components**:

1. **ModuleCard Component**:
```jsx
<ModuleCard
  title="JavaScript Fundamentals"
  description="Learn core JS concepts"
  progress={75}
  submodules={[...]}
  onStart={() => {}}
  onContinue={() => {}}
/>
```

2. **ChatBubble Component**:
```jsx
<ChatBubble
  message="Great progress!"
  sender="ai"
  timestamp="2024-01-15T10:30:00Z"
  progress={80}
  score={8.5}
/>
```

3. **ProgressRing Component**:
```jsx
<ProgressRing
  percentage={75}
  size="large"
  showLabel={true}
  animated={true}
/>
```

### Progressive Web App (PWA) Features

**PWA Capabilities**:
- Installable on mobile devices
- Offline functionality with service workers
- Background sync for conversation data
- Push notifications for training reminders
- App-like navigation and gestures

**Service Worker Strategy**:
- Cache-first for static assets
- Network-first for dynamic content
- Background sync for offline actions
- Push notification handling

### Accessibility Features

**WCAG 2.1 AA Compliance**:
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode
- Focus indicators
- Alternative text for images
- Semantic HTML structure

**Mobile Accessibility**:
- Voice control integration
- Large touch targets (minimum 44px)
- Gesture alternatives for all actions
- Reduced motion preferences
- Text scaling support

### Performance Optimizations

**Frontend Performance**:
- Code splitting with React.lazy()
- Image optimization with WebP format
- Lazy loading for non-critical components
- Virtual scrolling for large lists
- Debounced search and filtering
- Optimistic UI updates

**Bundle Optimization**:
- Tree shaking for unused code
- Gzip compression
- CDN delivery for static assets
- Critical CSS inlining
- Preloading of key resources