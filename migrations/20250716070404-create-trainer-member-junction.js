'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('trainer_member', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      trainer_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'trainers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      member_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'members',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      assigned_by: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'admins',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add unique constraint and indexes
    await queryInterface.addConstraint('trainer_member', {
      fields: ['trainer_id', 'member_id'],
      type: 'unique',
      name: 'unique_trainer_member'
    });
    
    await queryInterface.addIndex('trainer_member', ['trainer_id'], {
      name: 'trainer_member_trainer_id_index'
    });
    
    await queryInterface.addIndex('trainer_member', ['member_id'], {
      name: 'trainer_member_member_id_index'
    });
    
    await queryInterface.addIndex('trainer_member', ['assigned_by'], {
      name: 'trainer_member_assigned_by_index'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('trainer_member');
  }
};
