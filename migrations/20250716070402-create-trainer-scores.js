'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('trainer_scores', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      trainerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'trainers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      memberId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'members',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      assignedBy: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'admins',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      progress: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      },
      score: {
        type: Sequelize.DECIMAL(3, 1),
        allowNull: true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      isCompleted: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      },
      completedAt: {
        type: Sequelize.DATE,
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updated_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add indexes
    await queryInterface.addIndex('trainer_scores', ['trainerId', 'memberId'], {
      name: 'trainer_scores_trainer_member_index'
    });
    
    await queryInterface.addIndex('trainer_scores', ['progress'], {
      name: 'trainer_scores_progress_index'
    });
    
    await queryInterface.addIndex('trainer_scores', ['created_at'], {
      name: 'trainer_scores_created_at_index'
    });
    
    await queryInterface.addIndex('trainer_scores', ['assignedBy'], {
      name: 'trainer_scores_assigned_by_index'
    });
    
    await queryInterface.addIndex('trainer_scores', ['isCompleted'], {
      name: 'trainer_scores_is_completed_index'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('trainer_scores');
  }
};
