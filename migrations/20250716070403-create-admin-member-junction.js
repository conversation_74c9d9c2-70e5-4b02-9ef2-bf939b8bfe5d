'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('admin_member', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      admin_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'admins',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      member_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'members',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add unique constraint and indexes
    await queryInterface.addConstraint('admin_member', {
      fields: ['admin_id', 'member_id'],
      type: 'unique',
      name: 'unique_admin_member'
    });
    
    await queryInterface.addIndex('admin_member', ['admin_id'], {
      name: 'admin_member_admin_id_index'
    });
    
    await queryInterface.addIndex('admin_member', ['member_id'], {
      name: 'admin_member_member_id_index'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('admin_member');
  }
};
