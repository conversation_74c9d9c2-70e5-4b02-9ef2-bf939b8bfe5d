'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('trainer_conversations', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      trainerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'trainers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      memberId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'members',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      messageFrom: {
        type: Sequelize.ENUM('member', 'ai'),
        allowNull: false
      },
      message: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      aiProgress: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      aiScore: {
        type: Sequelize.DECIMAL(3, 1),
        allowNull: true
      },
      metadata: {
        type: Sequelize.JSON,
        allowNull: true
      },
      sessionId: {
        type: Sequelize.STRING(255),
        allowNull: true
      },
      created_at: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add indexes
    await queryInterface.addIndex('trainer_conversations', ['trainerId', 'memberId'], {
      name: 'trainer_conversations_trainer_member_index'
    });
    
    await queryInterface.addIndex('trainer_conversations', ['created_at'], {
      name: 'trainer_conversations_created_at_index'
    });
    
    await queryInterface.addIndex('trainer_conversations', ['messageFrom'], {
      name: 'trainer_conversations_message_from_index'
    });
    
    await queryInterface.addIndex('trainer_conversations', ['sessionId'], {
      name: 'trainer_conversations_session_id_index'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('trainer_conversations');
  }
};
