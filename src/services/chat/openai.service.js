const { OpenAI } = require('openai');
const config = require('../../config');
const logger = require('../../utils/logger');
const { circuitBreakerManager, RetryHandler } = require('../../utils/circuit-breaker');

class ChatOpenAIService {
  constructor() {
    this.openai = new OpenAI({ apiKey: config.openai.apiKey });
    this.circuitBreaker = circuitBreakerManager.getCircuitBreaker('openai', {
      failureThreshold: 5,
      resetTimeout: 60000,
      expectedErrors: [
        'rate limit exceeded',
        'quota exceeded',
        'invalid_request_error'
      ]
    });
    this.retryHandler = new RetryHandler({
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 10000,
      backoffFactor: 2,
      retryCondition: (error) => {
        return error.code === 'ECONNREFUSED' ||
               error.code === 'ETIMEDOUT' ||
               error.code === 'ENOTFOUND' ||
               (error.response && error.response.status >= 500) ||
               (error.response && error.response.status === 429);
      }
    });
  }

  /**
   * Call OpenAI API with circuit breaker and retry logic
   * @param {Array} messages - Messages array
   * @returns {Promise<Object>} - OpenAI response
   */
  async callOpenAIWithRetry(messages) {
    const openaiCall = async () => {
      return await this.openai.chat.completions.create({
        model: config.openai.model,
        messages: messages,
        max_tokens: config.openai.maxTokens,
        temperature: 0.7,
        response_format: { type: "json_object" }
      });
    };
    return await this.circuitBreaker.execute(async () => {
      return await this.retryHandler.execute(openaiCall);
    });
  }

  /**
   * Generate AI response using OpenAI API
   * @param {Object} trainer - Trainer object
   * @param {Array} conversationHistory - Conversation history
   * @param {Object} currentProgress - Current progress data
   * @param {Function} buildConversationContext - Function to build context
   * @returns {Promise<Object>} - AI response
   */
  async generateAIResponse(trainer, conversationHistory, currentProgress, buildConversationContext) {
    const startTime = Date.now();
    try {
      const messages = buildConversationContext(trainer, conversationHistory, currentProgress);
      const response = await this.callOpenAIWithRetry(messages);
      const processingTime = Date.now() - startTime;
      return {
        content: response.choices[0].message.content,
        tokensUsed: response.usage?.total_tokens || 0,
        processingTime
      };
    } catch (error) {
      logger.error('Error generating AI response:', error);
      return {
        content: JSON.stringify({
          message: "I apologize, but I'm experiencing technical difficulties. Please try again in a moment.",
          progress: currentProgress?.progress || 0,
          score: null
        }),
        tokensUsed: 0,
        processingTime: Date.now() - startTime
      };
    }
  }

  /**
   * Parse AI response to extract message, progress, and score
   * @param {Object} aiResponse - AI response object
   * @param {Function} validateProgress - Function to validate progress
   * @param {Function} validateScore - Function to validate score
   * @returns {Object} - Parsed response
   */
  parseAIResponse(aiResponse, validateProgress, validateScore) {
    try {
      const parsed = JSON.parse(aiResponse.content);
      return {
        message: parsed.message || 'Invalid response format',
        progress: validateProgress(parsed.progress),
        score: validateScore(parsed.score),
        tokensUsed: aiResponse.tokensUsed,
        processingTime: aiResponse.processingTime
      };
    } catch (error) {
      logger.error('Error parsing AI response:', error);
      return {
        message: aiResponse.content || 'Invalid response received',
        progress: null,
        score: null,
        tokensUsed: aiResponse.tokensUsed,
        processingTime: aiResponse.processingTime
      };
    }
  }
}

module.exports = ChatOpenAIService; 