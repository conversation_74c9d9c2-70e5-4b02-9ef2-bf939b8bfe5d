---
alwaysApply: true
---
# Panduan Struktur Proyek

Entry point utama backend adalah [src/server.js](mdc:src/server.js) yang menginisialisasi Express, WebSocket, koneksi database, dan cache. 

Aplikasi Express diatur di [src/app.js](mdc:src/app.js), yang memuat middleware, rute API, dan dokumentasi Swagger.

Konfigurasi global dan environment dikelola di [src/config/index.js](mdc:src/config/index.js) dan subfolder [src/config/](mdc:src/config/).

Struktur utama kode backend:
- [src/controllers/](mdc:src/controllers/): Controller untuk request API
- [src/services/](mdc:src/services/): Bisnis logic utama
- [src/models/](mdc:src/models/): Model database (Sequelize)
- [src/routes/](mdc:src/routes/): Definisi rute API
- [src/middleware/](mdc:src/middleware/): Middleware Express
- [src/utils/](mdc:src/utils/): Helper dan utilitas

Testing terletak di [tests/](mdc:tests/) dengan struktur unit dan integration test.
