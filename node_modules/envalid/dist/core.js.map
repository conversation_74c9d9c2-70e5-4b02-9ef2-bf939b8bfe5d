{"version": 3, "file": "core.js", "sourceRoot": "", "sources": ["../src/core.ts"], "names": [], "mappings": ";;;AAAA,mCAAoD;AAEpD,uCAA4C;AAE/B,QAAA,cAAc,GAAG,MAAM,CAAC,qBAAqB,CAAC,CAAA;AAE3D;;;;;GAKG;AACH,SAAS,WAAW,CAAI,EAQvB;QAPC,IAAI,UAAA,EACJ,IAAI,UAAA,EACJ,QAAQ,cAAA;IAMR,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU,EAAE;QACrC,MAAM,IAAI,iBAAQ,CAAC,6BAAqB,IAAI,OAAG,CAAC,CAAA;KACjD;IACD,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,QAAkB,CAAC,CAAA;IAE7C,IAAI,IAAI,CAAC,OAAO,EAAE;QAChB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YAChC,MAAM,IAAI,SAAS,CAAC,sDAA4C,IAAI,QAAI,CAAC,CAAA;SAC1E;aAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACxC,MAAM,IAAI,iBAAQ,CAAC,kBAAU,KAAK,gCAAqB,IAAI,CAAC,OAAO,MAAG,CAAC,CAAA;SACxE;KACF;IACD,IAAI,KAAK,IAAI,IAAI;QAAE,MAAM,IAAI,iBAAQ,CAAC,sCAA8B,IAAI,OAAG,CAAC,CAAA;IAC5E,OAAO,KAAK,CAAA;AACd,CAAC;AAED,uEAAuE;AACvE,SAAgB,qBAAqB,CAAI,IAAa;IACpD,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,kBAAU,IAAI,CAAC,OAAO,QAAI,CAAC,CAAC,CAAC,EAAE,CAAA;IAC7D,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAS,IAAI,CAAC,IAAI,CAAE,CAAC,CAAC,CAAC,EAAE,CAAA;IACtD,OAAO,UAAG,IAAI,CAAC,IAAI,SAAG,MAAM,SAAG,QAAQ,CAAE,CAAA;AAC3C,CAAC;AAJD,sDAIC;AAED,IAAM,eAAe,GAAG,UAAI,GAAY,EAAE,CAAuB;IAC/D,OAAQ,GAAW,CAAC,CAAC,CAAC,CAAA;AACxB,CAAC,CAAA;AAED,IAAM,gBAAgB,GAAG,UAAC,KAAU,IAAsB,OAAA,KAAK,KAAK,sBAAc,EAAxB,CAAwB,CAAA;AAElF;;GAEG;AACH,SAAgB,eAAe,CAC7B,WAAoB,EACpB,KAAQ,EACR,OAA0C;IAA1C,wBAAA,EAAA,YAA0C;IAE1C,IAAM,UAAU,GAAG,EAAoB,CAAA;IACvC,IAAM,WAAW,GAAG,KAA2D,CAAA;IAC/E,IAAM,MAAM,GAAG,EAA4B,CAAA;IAC3C,IAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAmB,CAAA;IAC1D,IAAM,UAAU,GAAG,eAAe,CAAC,WAAW,EAAE,UAAU,CAAC,CAAA;IAE3D,KAAgB,UAAO,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO,EAAE;QAApB,IAAM,CAAC,gBAAA;QACV,IAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;QAC3B,IAAM,QAAQ,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC,CAAC,CAAA;QAEhD,IAAI;YACF,6FAA6F;YAC7F,8CAA8C;YAC9C,IAAI,QAAQ,KAAK,SAAS,EAAE;gBAC1B,oFAAoF;gBACpF,IAAM,eAAe,GACnB,UAAU,IAAI,UAAU,KAAK,YAAY,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,YAAY,CAAC,CAAA;gBAEhF,IAAI,eAAe,EAAE;oBACnB,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAA;oBAE/B,IAAI,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,UAAU,IAAI,MAAM,EAAE;wBAC7D,MAAM,IAAI,wBAAe,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAA;qBACvD;oBAED,SAAQ;iBACT;gBAED,IAAI,SAAS,IAAI,IAAI,EAAE;oBACrB,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAA;oBAC5B,SAAQ;iBACT;gBAED,gDAAgD;gBAChD,UAAU,CAAC,CAAC,CAAC,GAAG,SAAS,CAAA;gBACzB,MAAM,IAAI,wBAAe,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAA;aACvD;YAED,UAAU,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,IAAI,EAAE,CAAW,EAAE,IAAI,MAAA,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAA;SACnE;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,MAAK,IAAI;gBAAE,MAAM,GAAG,CAAA;YACzC,IAAI,GAAG,YAAY,KAAK;gBAAE,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAA;SAC1C;KACF;IAED,8FAA8F;IAC9F,6EAA6E;IAC7E,KAAgB,UAAO,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO,EAAE;QAApB,IAAM,CAAC,gBAAA;QACV,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,SAAS,EAAE;YAC1B,IAAM,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;YAC3B,IACE,UAAU,CAAC,CAAC,CAAC,IAAI,SAAS;gBAC1B,IAAI,CAAC,YAAY,KAAK,SAAS;gBAC/B,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B;gBACA,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,wBAAe,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAA;aAC7D;SACF;KACF;IAED,IAAM,QAAQ,GAAG,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,KAAI,0BAAe,CAAA;IACrD,QAAQ,CAAC,EAAE,MAAM,QAAA,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,CAAA;IACrC,OAAO,UAAU,CAAA;AACnB,CAAC;AApED,0CAoEC"}