"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSanitizedEnv = exports.formatSpecDescription = exports.testOnlySymbol = void 0;
var errors_1 = require("./errors");
var reporter_1 = require("./reporter");
exports.testOnlySymbol = Symbol('envalid - test only');
/**
 * Validate a single env var, given a spec object
 *
 * @throws EnvError - If validation is unsuccessful
 * @return - The cleaned value
 */
function validateVar(_a) {
    var spec = _a.spec, name = _a.name, rawValue = _a.rawValue;
    if (typeof spec._parse !== 'function') {
        throw new errors_1.EnvError("Invalid spec for \"".concat(name, "\""));
    }
    var value = spec._parse(rawValue);
    if (spec.choices) {
        if (!Array.isArray(spec.choices)) {
            throw new TypeError("\"choices\" must be an array (in spec for \"".concat(name, "\")"));
        }
        else if (!spec.choices.includes(value)) {
            throw new errors_1.EnvError("Value \"".concat(value, "\" not in choices [").concat(spec.choices, "]"));
        }
    }
    if (value == null)
        throw new errors_1.EnvError("Invalid value for env var \"".concat(name, "\""));
    return value;
}
// Format a string error message for when a required env var is missing
function formatSpecDescription(spec) {
    var egText = spec.example ? " (eg. \"".concat(spec.example, "\")") : '';
    var docsText = spec.docs ? ". See ".concat(spec.docs) : '';
    return "".concat(spec.desc).concat(egText).concat(docsText);
}
exports.formatSpecDescription = formatSpecDescription;
var readRawEnvValue = function (env, k) {
    return env[k];
};
var isTestOnlySymbol = function (value) { return value === exports.testOnlySymbol; };
/**
 * Perform the central validation/sanitization logic on the full environment object
 */
function getSanitizedEnv(environment, specs, options) {
    if (options === void 0) { options = {}; }
    var cleanedEnv = {};
    var castedSpecs = specs;
    var errors = {};
    var varKeys = Object.keys(castedSpecs);
    var rawNodeEnv = readRawEnvValue(environment, 'NODE_ENV');
    for (var _i = 0, varKeys_1 = varKeys; _i < varKeys_1.length; _i++) {
        var k = varKeys_1[_i];
        var spec = castedSpecs[k];
        var rawValue = readRawEnvValue(environment, k);
        try {
            // If no value was given and default/devDefault were provided, return the appropriate default
            // value without passing it through validation
            if (rawValue === undefined) {
                // Use devDefault values only if NODE_ENV was explicitly set, and isn't 'production'
                var usingDevDefault = rawNodeEnv && rawNodeEnv !== 'production' && Object.hasOwn(spec, 'devDefault');
                if (usingDevDefault) {
                    cleanedEnv[k] = spec.devDefault;
                    if (isTestOnlySymbol(spec.devDefault) && rawNodeEnv != 'test') {
                        throw new errors_1.EnvMissingError(formatSpecDescription(spec));
                    }
                    continue;
                }
                if ('default' in spec) {
                    cleanedEnv[k] = spec.default;
                    continue;
                }
                // Throw error when no default value is provided
                cleanedEnv[k] = undefined;
                throw new errors_1.EnvMissingError(formatSpecDescription(spec));
            }
            cleanedEnv[k] = validateVar({ name: k, spec: spec, rawValue: rawValue });
        }
        catch (err) {
            if ((options === null || options === void 0 ? void 0 : options.reporter) === null)
                throw err;
            if (err instanceof Error)
                errors[k] = err;
        }
    }
    // This block is for supporting requiredWhen. If that field was provided for a var's spec and 
    // its condition evaluates to a truthy value, ensure that env var is present.
    for (var _a = 0, varKeys_2 = varKeys; _a < varKeys_2.length; _a++) {
        var k = varKeys_2[_a];
        if (errors[k] == undefined) {
            var spec = castedSpecs[k];
            if (cleanedEnv[k] == undefined &&
                spec.requiredWhen !== undefined &&
                spec.requiredWhen(cleanedEnv)) {
                errors[k] = new errors_1.EnvMissingError(formatSpecDescription(spec));
            }
        }
    }
    var reporter = (options === null || options === void 0 ? void 0 : options.reporter) || reporter_1.defaultReporter;
    reporter({ errors: errors, env: cleanedEnv });
    return cleanedEnv;
}
exports.getSanitizedEnv = getSanitizedEnv;
//# sourceMappingURL=core.js.map