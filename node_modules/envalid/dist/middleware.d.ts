import type { CleanedEnvAccessors, StrictProxyMiddlewareOptions } from './types';
export declare const strictProxyMiddleware: <T extends object>(envObj: T, rawEnv: unknown, options?: StrictProxyMiddlewareOptions) => T;
export declare const accessorMiddleware: <T>(envObj: T, rawEnv: unknown) => T & CleanedEnvAccessors;
export declare const applyDefaultMiddleware: <T>(cleanedEnv: T, rawEnv: unknown) => T & CleanedEnvAccessors;
