"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.json = exports.url = exports.port = exports.host = exports.email = exports.str = exports.num = exports.bool = void 0;
var errors_1 = require("./errors");
var makers_1 = require("./makers");
// Simplified adaptation of https://github.com/validatorjs/validator.js/blob/master/src/lib/isFQDN.js
var isFQDN = function (input) {
    if (!input.length)
        return false;
    var parts = input.split('.');
    for (var part = void 0, i = 0; i < parts.length; i++) {
        part = parts[i];
        if (!/^[a-z\u00a1-\uffff0-9-]+$/i.test(part))
            return false;
        if (/[\uff01-\uff5e]/.test(part))
            return false; // disallow full-width chars
        if (part[0] === '-' || part[part.length - 1] === '-')
            return false;
    }
    return true;
};
// "best effort" regex-based IP address check
// If you want a more exhaustive check, create your own custom validator, perhaps wrapping this
// implementation (the source of the ipv4 regex below): https://github.com/validatorjs/validator.js/blob/master/src/lib/isIP.js
var ipv4Regex = /^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$/;
var ipv6Regex = /([a-f0-9]+:+)+[a-f0-9]+/;
var isIP = function (input) {
    if (!input.length)
        return false;
    return ipv4Regex.test(input) || ipv6Regex.test(input);
};
var EMAIL_REGEX = /^[^@\s]+@[^@\s]+\.[^@\s]+$/; // intentionally non-exhaustive
// We use exact validator here because narrowing down to either 'true' or 'false'
// makes no sense.
exports.bool = (0, makers_1.makeExactValidator)(function (input) {
    switch (input) {
        case true:
        case 'true':
        case 't':
        case 'yes':
        case 'on':
        case '1':
            return true;
        case false:
        case 'false':
        case 'f':
        case 'no':
        case 'off':
        case '0':
            return false;
        default:
            throw new errors_1.EnvError("Invalid bool input: \"".concat(input, "\""));
    }
});
exports.num = (0, makers_1.makeValidator)(function (input) {
    var coerced = parseFloat(input);
    if (Number.isNaN(coerced))
        throw new errors_1.EnvError("Invalid number input: \"".concat(input, "\""));
    return coerced;
});
exports.str = (0, makers_1.makeValidator)(function (input) {
    if (typeof input === 'string')
        return input;
    throw new errors_1.EnvError("Not a string: \"".concat(input, "\""));
});
exports.email = (0, makers_1.makeValidator)(function (x) {
    if (EMAIL_REGEX.test(x))
        return x;
    throw new errors_1.EnvError("Invalid email address: \"".concat(x, "\""));
});
exports.host = (0, makers_1.makeValidator)(function (input) {
    if (!isFQDN(input) && !isIP(input)) {
        throw new errors_1.EnvError("Invalid host (domain or ip): \"".concat(input, "\""));
    }
    return input;
});
exports.port = (0, makers_1.makeValidator)(function (input) {
    var coerced = +input;
    if (Number.isNaN(coerced) ||
        "".concat(coerced) !== "".concat(input) ||
        coerced % 1 !== 0 ||
        coerced < 1 ||
        coerced > 65535) {
        throw new errors_1.EnvError("Invalid port input: \"".concat(input, "\""));
    }
    return coerced;
});
exports.url = (0, makers_1.makeValidator)(function (x) {
    try {
        new URL(x);
        return x;
    }
    catch (e) {
        throw new errors_1.EnvError("Invalid url: \"".concat(x, "\""));
    }
});
/**
 * Unless passing a default property, it's recommended that you provide an explicit type parameter
 * for json validation if you're using TypeScript. Otherwise the output will be typed as `any`.
 * For example:
 *
 * ```ts
 * cleanEnv({
 *   MY_VAR: json<{ foo: number }>(),
 * })
 * ```
 */
exports.json = (0, makers_1.makeStructuredValidator)(function (x) {
    try {
        return JSON.parse(x);
    }
    catch (e) {
        throw new errors_1.EnvError("Invalid json: \"".concat(x, "\""));
    }
});
//# sourceMappingURL=validators.js.map