"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.applyDefaultMiddleware = exports.accessorMiddleware = exports.strictProxyMiddleware = void 0;
var strictProxyMiddleware = function (envObj, rawEnv, options) {
    if (options === void 0) { options = {}; }
    var _a = options.extraInspectables, extraInspectables = _a === void 0 ? [] : _a;
    var inspectables = [
        'length',
        'inspect',
        'hasOwnProperty',
        'toJSON',
        Symbol.toStringTag,
        Symbol.iterator,
        // For jest
        'asymmetricMatch',
        'nodeType',
        // For react-refresh, see #150
        '$$typeof',
        // For libs that use `then` checks to see if objects are Promises (see #74):
        'then',
        // For usage with TypeScript esModuleInterop flag
        '__esModule',
    ];
    var inspectSymbolStrings = ['Symbol(util.inspect.custom)', 'Symbol(nodejs.util.inspect.custom)'];
    return new Proxy(envObj, {
        get: function (target, name) {
            var _a;
            // These checks are needed because calling console.log on a
            // proxy that throws crashes the entire process. This permits access on
            // the necessary properties for `console.log(envObj)`, `envObj.length`,
            // `envObj.hasOwnProperty('string')` to work.
            if (inspectables.includes(name) ||
                inspectSymbolStrings.includes(name.toString()) ||
                extraInspectables.includes(name)) {
                // @ts-expect-error TS doesn't like symbol types as indexers
                return target[name];
            }
            var varExists = Object.hasOwn(target, name);
            if (!varExists) {
                if (typeof rawEnv === 'object' && ((_a = rawEnv === null || rawEnv === void 0 ? void 0 : rawEnv.hasOwnProperty) === null || _a === void 0 ? void 0 : _a.call(rawEnv, name))) {
                    throw new ReferenceError("[envalid] Env var ".concat(name, " was accessed but not validated. This var is set in the environment; please add an envalid validator for it."));
                }
                throw new ReferenceError("[envalid] Env var not found: ".concat(name));
            }
            return target[name];
        },
        set: function (_target, name) {
            throw new TypeError("[envalid] Attempt to mutate environment value: ".concat(name));
        },
    });
};
exports.strictProxyMiddleware = strictProxyMiddleware;
var accessorMiddleware = function (envObj, rawEnv) {
    // Attach is{Prod/Dev/Test} properties for more readable NODE_ENV checks
    // Note that isDev and isProd are just aliases to isDevelopment and isProduction
    // @ts-ignore attempt to read NODE_ENV even if it's not in the spec
    var computedNodeEnv = envObj.NODE_ENV || rawEnv.NODE_ENV;
    // If NODE_ENV is not set, assume production
    var isProd = !computedNodeEnv || computedNodeEnv === 'production';
    Object.defineProperties(envObj, {
        isDevelopment: { value: computedNodeEnv === 'development' },
        isDev: { value: computedNodeEnv === 'development' },
        isProduction: { value: isProd },
        isProd: { value: isProd },
        isTest: { value: computedNodeEnv === 'test' },
    });
    return envObj;
};
exports.accessorMiddleware = accessorMiddleware;
var applyDefaultMiddleware = function (cleanedEnv, rawEnv) {
    // Note: Ideally we would declare the default middlewares in an array and apply them in series with
    // a generic pipe() function. However, a generically typed variadic pipe() appears to not be possible
    // in TypeScript as of 4.x, so we just manually apply them below. See
    // https://github.com/microsoft/TypeScript/pull/39094#issuecomment-647042984
    return (0, exports.strictProxyMiddleware)((0, exports.accessorMiddleware)(cleanedEnv, rawEnv), rawEnv);
};
exports.applyDefaultMiddleware = applyDefaultMiddleware;
//# sourceMappingURL=middleware.js.map