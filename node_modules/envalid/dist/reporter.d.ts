import type { ReporterOptions } from './types';
type Errors<T> = Partial<Record<keyof T, Error>>;
type Logger = (data: any, ...args: any[]) => void;
type ExtraOptions<T> = {
    onError?: (errors: Errors<T>) => void;
    logger: (output: string) => void;
};
export declare const envalidErrorFormatter: <T = any>(errors: Partial<Record<keyof T, Error>>, logger?: Logger) => void;
export declare const defaultReporter: <T = any>({ errors }: ReporterOptions<T>, { onError, logger }?: ExtraOptions<T>) => void;
export {};
