"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testOnly = exports.customCleanEnv = exports.cleanEnv = void 0;
var core_1 = require("./core");
var middleware_1 = require("./middleware");
/**
 * Returns a sanitized, immutable environment object. _Only_ the env vars
 * specified in the `validators` parameter will be accessible on the returned
 * object.
 * @param environment An object containing your env vars (eg. process.env).
 * @param specs An object that specifies the format of required vars.
 * @param options An object that specifies options for cleanEnv.
 */
function cleanEnv(environment, specs, options) {
    if (options === void 0) { options = {}; }
    var cleaned = (0, core_1.getSanitizedEnv)(environment, specs, options);
    return Object.freeze((0, middleware_1.applyDefaultMiddleware)(cleaned, environment));
}
exports.cleanEnv = cleanEnv;
/**
 * Returns a sanitized, immutable environment object, and passes it through a custom
 * applyMiddleware function before being frozen. Most users won't need the flexibility of custom
 * middleware; prefer cleanEnv() unless you're sure you need it
 *
 * @param environment An object containing your env vars (eg. process.env).
 * @param specs An object that specifies the format of required vars.
 * @param applyMiddleware A function that applies transformations to the cleaned env object
 * @param options An object that specifies options for cleanEnv.
 */
function customCleanEnv(environment, specs, applyMiddleware, options) {
    if (options === void 0) { options = {}; }
    var cleaned = (0, core_1.getSanitizedEnv)(environment, specs, options);
    return Object.freeze(applyMiddleware(cleaned, environment));
}
exports.customCleanEnv = customCleanEnv;
/**
 * Utility function for providing default values only when NODE_ENV=test
 *
 * For more context, see https://github.com/af/envalid/issues/32
 */
var testOnly = function (defaultValueForTests) {
    return process.env.NODE_ENV === 'test' ? defaultValueForTests : core_1.testOnlySymbol; // T is not strictly correct, but prevents type errors during usage
};
exports.testOnly = testOnly;
//# sourceMappingURL=envalid.js.map