"use strict";
// Surprisingly involved error subclassing
// See https://stackoverflow.com/questions/41102060/typescript-extending-error-class
Object.defineProperty(exports, "__esModule", { value: true });
exports.EnvMissingError = exports.EnvError = void 0;
var tslib_1 = require("tslib");
var EnvError = /** @class */ (function (_super) {
    tslib_1.__extends(EnvError, _super);
    function EnvError(message) {
        var _newTarget = this.constructor;
        var _this = _super.call(this, message) || this;
        Object.setPrototypeOf(_this, _newTarget.prototype);
        Error.captureStackTrace(_this, EnvError);
        _this.name = _this.constructor.name;
        return _this;
    }
    return EnvError;
}(TypeError));
exports.EnvError = EnvError;
var EnvMissingError = /** @class */ (function (_super) {
    tslib_1.__extends(EnvMissingError, _super);
    function EnvMissingError(message) {
        var _newTarget = this.constructor;
        var _this = _super.call(this, message) || this;
        Object.setPrototypeOf(_this, _newTarget.prototype);
        Error.captureStackTrace(_this, EnvMissingError);
        _this.name = _this.constructor.name;
        return _this;
    }
    return EnvMissingError;
}(ReferenceError));
exports.EnvMissingError = EnvMissingError;
//# sourceMappingURL=errors.js.map