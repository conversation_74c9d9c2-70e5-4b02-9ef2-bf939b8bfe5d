{"name": "envalid", "version": "8.1.0", "description": "Validation for your environment variables", "main": "dist/index.js", "types": "dist/index.d.ts", "sideEffects": false, "engines": {"node": ">=18"}, "engineStrict": true, "files": ["dist", "src"], "scripts": {"check-exports": "attw --pack .", "start": "tsc --watch", "prepare": "tsc", "coverage": "vitest run --coverage", "tsc": "tsc --noEmit", "lint": "biome lint --diagnostic-level=error src", "test": "vitest", "test:ci": "vitest run"}, "repository": {"type": "git", "url": "https://github.com/af/envalid"}, "keywords": [".env", "dotenv", "env", "env var", "environment", "environment variable", "validation"], "dependencies": {"tslib": "2.8.1"}, "devDependencies": {"@arethetypeswrong/cli": "0.18.2", "@biomejs/biome": "2.0.6", "@types/node": "24.0.13", "@vitest/coverage-v8": "3.2.4", "expect-type": "1.2.2", "husky": "9.1.7", "typescript": "5.2.2", "vitest": "3.2.4"}, "author": "<PERSON>", "license": "MIT", "husky": {"hooks": {"pre-commit": "yarn lint && yarn tsc", "pre-push": "yarn test"}}}